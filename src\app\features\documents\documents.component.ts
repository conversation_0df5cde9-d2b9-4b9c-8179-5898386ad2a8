import { Component, OnInit, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { MatTabsModule } from '@angular/material/tabs';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatCardModule } from '@angular/material/card';
import { MatDialog } from '@angular/material/dialog';


// Core imports
import { IBreadcrumbItem } from '@core/gl-interfaces/IBreadcrumbItem/ibreadcrumb-item';
import { SizeEnum } from '@shared/enum/size-enum';
import { BreadcrumbComponent } from '@shared/components/breadcrumb/breadcrumb.component';
import { CustomButtonComponent } from '@shared/components/custom-button/custom-button.component';
import { ButtonTypeEnum, IconEnum } from '@core/enums/icon-enum';

// Feature components
import { DocumentListComponent } from './components/document-list/document-list.component';
import { DocumentUploadComponent } from './components/document-upload/document-upload.component';

// Services and API
import { TokenService } from '../auth/services/token.service';
import {
  DocumentServiceProxy,
  DocumentCategoryDto,
  GetDocumentCategoriesQuery
} from '@core/api/api.generated';
import { ErrorModalService } from '@core/services/error-modal.service';
import { PageHeaderComponent } from '@shared/components/page-header/page-header.component';

@Component({
  selector: 'app-documents',
  standalone: true,
  imports: [
    CommonModule,
    TranslateModule,
    MatTabsModule,
    MatButtonModule,
    MatIconModule,
    MatProgressSpinnerModule,
    MatCardModule,
    BreadcrumbComponent,
    CustomButtonComponent,
    DocumentListComponent,
    DocumentUploadComponent,
    PageHeaderComponent
  ],
  templateUrl: './documents.component.html',
  styleUrls: ['./documents.component.scss']
})
export class DocumentsComponent implements OnInit {
    @ViewChild(DocumentListComponent) list!: DocumentListComponent;

  // Breadcrumb configuration
  breadcrumbItems: IBreadcrumbItem[] = [];
  breadcrumbSizeEnum = SizeEnum;

  // Component state
  isLoading = false;
  currentFundId: number | null = null;
  selectedTabIndex = 0;

  // Document categories (loaded from API)
  documentCategories: DocumentCategoryDto[] = [];

  // Track which tabs have been loaded to implement lazy loading
  loadedTabs = new Set<number>();

  // UI enums
  buttonEnum = ButtonTypeEnum;
  iconEnum = IconEnum;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private translateService: TranslateService,
    private documentProxy: DocumentServiceProxy,
    private errorModalService: ErrorModalService,
    public tokenService: TokenService,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.initializeBreadcrumb();
    this.loadRouteParams();
    this.loadDocumentCategories();
  }

  private initializeBreadcrumb(): void {
    this.breadcrumbItems = [
      {
        label: this.translateService.instant('COMMON.HOME'),
        url: '/admin/dashboard'
      },
      {
        label: this.translateService.instant('DOCUMENTS.TITLE'),
        url: '/admin/documents'
      }
    ];
  }

  private loadRouteParams(): void {
    this.route.queryParams.subscribe(params => {
      if (params['fundId']) {
        this.currentFundId = +params['fundId'];
        this.updateBreadcrumbWithFund();
      }
    });
  }

  private loadDocumentCategories(): void {
    this.isLoading = true;

    // Use NSwag-generated proxy with proper typing
    const query = new GetDocumentCategoriesQuery();

    this.documentProxy.categories(query).subscribe({
      next: (response) => {
        // Response is properly typed as DocumentCategoryDtoListBaseResponse
        if (response?.data && Array.isArray(response.data)) {
          this.documentCategories = response.data;
        } else {
          this.documentCategories = [];
        }
        this.isLoading = false;

        // If no categories are returned, show appropriate message
        if (this.documentCategories.length === 0) {
          console.warn('No document categories returned from API');
        } else {
          // Mark the first tab as loaded by default
          this.loadedTabs.add(0);
        }
      },
      error: (error: any) => {
        console.error('Error loading document categories:', error);
        this.errorModalService.showError(this.translateService.instant('DOCUMENTS.ERRORS.LOAD_CATEGORIES_FAILED'));
        this.documentCategories = [];
        this.isLoading = false;
      }
    });
  }

  private updateBreadcrumbWithFund(): void {
    if (this.currentFundId) {
      this.breadcrumbItems = [
        {
          label: this.translateService.instant('COMMON.HOME'),
          url: '/admin/dashboard'
        },
        {
          label: this.translateService.instant('INVESTMENT_FUNDS.TITLE'),
          url: '/admin/investment-funds'
        },
        {
          label: this.translateService.instant('BREADCRUMB.FUND_DETAILS'),
          url: `/admin/investment-funds/fund-details?id=${this.currentFundId}`
        },
        {
          label: this.translateService.instant('DOCUMENTS.TITLE'),
          url: `/admin/documents/fund/${this.currentFundId}`,
          disabled: true
        }
      ];
    }
  }

  onTabChange(index: number): void {
    this.selectedTabIndex = index;
    // Mark this tab as loaded for lazy loading
    if (!this.loadedTabs.has(index)) {
      this.loadedTabs.add(index);
    }
  }

  onUploadDocument(): void {
    const dialogRef = this.dialog.open(DocumentUploadComponent, {
      width: '600px',
      data: {
        fundId: this.currentFundId,
        selectedCategory: this.getCurrentCategory(),
        documentCategories: this.documentCategories // Pass categories to avoid duplicate API calls
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        // Refresh the current tab's document list
        this.refreshCurrentTab();
      }
    });
  }

  onBreadcrumbClicked(item: IBreadcrumbItem): void {
    if (item.url) {
      this.router.navigate([item.url]);
    }
  }

  getCurrentCategory(): any {
    return this.documentCategories[this.selectedTabIndex];
  }

  private refreshCurrentTab(): void {
    this.list.loadDocuments();
  }
  onSearch(search:any)
  {
    this.list.loadDocuments(search);
  }
}
